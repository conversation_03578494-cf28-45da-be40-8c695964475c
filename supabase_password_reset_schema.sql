-- =====================================================
-- SUPABASE PASSWORD RESET SCHEMA
-- =====================================================
-- This file contains SQL schema for password reset functionality
-- including audit logging and security policies.
-- 
-- INSTRUCTIONS:
-- 1. Copy and paste these SQL commands into your Supabase SQL editor
-- 2. Execute them in the order provided
-- 3. Test the password reset functionality
-- =====================================================

-- =====================================================
-- 1. CREATE PASSWORD RESET AUDIT TABLE
-- =====================================================
-- This table logs all password reset attempts for security auditing

CREATE TABLE IF NOT EXISTS public.password_reset_audit (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID,
    email TEXT NOT NULL,
    reset_type VARCHAR(50) NOT NULL DEFAULT 'password_reset',
    status VARCHAR(50) NOT NULL DEFAULT 'requested',
    ip_address INET,
    user_agent TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    additional_data JSONB DEFAULT '{}'::jsonb
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_email ON public.password_reset_audit(email);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_requested_at ON public.password_reset_audit(requested_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_status ON public.password_reset_audit(status);

-- =====================================================
-- 2. CREATE PASSWORD RESET REQUEST FUNCTION
-- =====================================================
-- This function handles password reset requests with audit logging

CREATE OR REPLACE FUNCTION public.request_password_reset(
    p_email TEXT,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_exists BOOLEAN := FALSE;
    user_id_found UUID;
    reset_result JSON;
BEGIN
    -- Check if user exists (don't reveal if user doesn't exist for security)
    SELECT id INTO user_id_found 
    FROM auth.users 
    WHERE email = p_email AND email_confirmed_at IS NOT NULL;
    
    user_exists := (user_id_found IS NOT NULL);
    
    -- Log the password reset request (always log, even for non-existent users)
    INSERT INTO public.password_reset_audit (
        user_id,
        email,
        reset_type,
        status,
        ip_address,
        user_agent,
        expires_at,
        additional_data
    ) VALUES (
        user_id_found,
        p_email,
        'password_reset',
        CASE WHEN user_exists THEN 'requested' ELSE 'invalid_email' END,
        p_ip_address,
        p_user_agent,
        NOW() + INTERVAL '1 hour',
        json_build_object(
            'user_exists', user_exists,
            'request_timestamp', NOW()
        )
    );
    
    -- Always return success to prevent email enumeration attacks
    reset_result := json_build_object(
        'success', true,
        'message', 'If an account with this email exists, you will receive a password reset link.',
        'email', p_email,
        'requested_at', NOW()
    );
    
    RETURN reset_result;
END;
$$;

-- =====================================================
-- 3. CREATE PASSWORD RESET COMPLETION FUNCTION
-- =====================================================
-- This function logs successful password reset completions

CREATE OR REPLACE FUNCTION public.complete_password_reset(
    p_email TEXT,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_id_found UUID;
    completion_result JSON;
BEGIN
    -- Get user ID
    SELECT id INTO user_id_found 
    FROM auth.users 
    WHERE email = p_email;
    
    -- Log the password reset completion
    INSERT INTO public.password_reset_audit (
        user_id,
        email,
        reset_type,
        status,
        ip_address,
        user_agent,
        completed_at,
        additional_data
    ) VALUES (
        user_id_found,
        p_email,
        'password_reset',
        'completed',
        p_ip_address,
        p_user_agent,
        NOW(),
        json_build_object(
            'completion_timestamp', NOW(),
            'method', 'password_reset_completion'
        )
    );
    
    -- Update previous pending requests as completed
    UPDATE public.password_reset_audit 
    SET 
        status = 'completed',
        completed_at = NOW(),
        additional_data = additional_data || json_build_object('auto_completed', true)
    WHERE 
        email = p_email 
        AND status = 'requested' 
        AND requested_at > NOW() - INTERVAL '24 hours';
    
    completion_result := json_build_object(
        'success', true,
        'message', 'Password reset completed successfully',
        'email', p_email,
        'completed_at', NOW()
    );
    
    RETURN completion_result;
END;
$$;

-- =====================================================
-- 4. CREATE PASSWORD RESET CLEANUP FUNCTION
-- =====================================================
-- This function cleans up old password reset audit logs

CREATE OR REPLACE FUNCTION public.cleanup_password_reset_audit(
    days_to_keep INTEGER DEFAULT 30
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.password_reset_audit 
    WHERE requested_at < NOW() - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- =====================================================
-- 5. SET UP ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on password reset audit table
ALTER TABLE public.password_reset_audit ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own password reset audit logs
CREATE POLICY "Users can view own password reset logs" ON public.password_reset_audit
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        (user_id = auth.uid() OR email = auth.email())
    );

-- Policy: Only system can insert password reset audit logs (handled by functions)
CREATE POLICY "System can insert password reset logs" ON public.password_reset_audit
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- 6. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant execute permission on RPC functions to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.request_password_reset(TEXT, INET, TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.complete_password_reset(TEXT, INET, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_password_reset_audit(INTEGER) TO authenticated;

-- Grant necessary permissions on audit table
GRANT SELECT ON public.password_reset_audit TO authenticated;
GRANT INSERT ON public.password_reset_audit TO authenticated;

-- =====================================================
-- 7. CREATE RATE LIMITING VIEW (Optional)
-- =====================================================
-- This view helps track password reset request frequency

CREATE OR REPLACE VIEW public.password_reset_rate_limit AS
SELECT 
    email,
    COUNT(*) as request_count,
    MAX(requested_at) as last_request,
    MIN(requested_at) as first_request
FROM public.password_reset_audit 
WHERE 
    requested_at > NOW() - INTERVAL '1 hour'
    AND status IN ('requested', 'invalid_email')
GROUP BY email
HAVING COUNT(*) >= 3;

-- Grant access to rate limiting view
GRANT SELECT ON public.password_reset_rate_limit TO authenticated, anon;

-- =====================================================
-- 8. TEST FUNCTIONS (Run these to test the setup)
-- =====================================================

-- Test 1: Request password reset
-- SELECT public.request_password_reset('<EMAIL>', '127.0.0.1'::inet, 'Test User Agent');

-- Test 2: Complete password reset
-- SELECT public.complete_password_reset('<EMAIL>', '127.0.0.1'::inet, 'Test User Agent');

-- Test 3: View audit logs
-- SELECT * FROM public.password_reset_audit ORDER BY requested_at DESC LIMIT 10;

-- Test 4: Check rate limiting
-- SELECT * FROM public.password_reset_rate_limit;

-- =====================================================
-- NOTES FOR IMPLEMENTATION:
-- =====================================================
-- 
-- 1. The request_password_reset() function can be called from Flutter using:
--    await supabase.rpc('request_password_reset', {'p_email': email});
--
-- 2. The complete_password_reset() function should be called after successful reset:
--    await supabase.rpc('complete_password_reset', {'p_email': email});
--
-- 3. All functions return JSON responses with success/error status
--
-- 4. Security features:
--    - Prevents email enumeration attacks
--    - Rate limiting view for monitoring
--    - Comprehensive audit logging
--    - Row Level Security for data protection
--
-- 5. The audit table tracks:
--    - All password reset requests (successful and failed)
--    - IP addresses and user agents for security
--    - Completion timestamps
--    - Expiration times for cleanup
--
-- =====================================================
