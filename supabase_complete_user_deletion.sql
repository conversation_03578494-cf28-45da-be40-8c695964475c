-- =====================================================
-- COMPLETE USER DELETION WITH AUTH RECORD REMOVAL
-- =====================================================
-- This SQL script provides complete user account deletion
-- including removal from Supabase Auth (auth.users table).
-- 
-- IMPORTANT: This requires elevated privileges and should be
-- executed by a database admin or service role.
-- =====================================================

-- =====================================================
-- 1. CREATE ADMIN-LEVEL USER DELETION FUNCTION
-- =====================================================
-- This function has elevated privileges to delete auth users

CREATE OR REPLACE FUNCTION public.admin_delete_user_account(
    target_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
    current_user_id UUID;
    target_user_email TEXT;
    affected_rows INTEGER := 0;
    temp_row_count INTEGER;
    deletion_result JSON;
BEGIN
    -- Get the current authenticated user
    current_user_id := auth.uid();
    
    -- Security check: Only allow users to delete their own account
    -- OR allow admin users (you can modify this logic as needed)
    IF current_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'unauthorized',
            'message', 'User must be authenticated to delete account'
        );
    END IF;
    
    -- For self-deletion, ensure target matches current user
    IF target_user_id != current_user_id THEN
        -- Check if current user is admin (modify this logic as needed)
        -- For now, only allow self-deletion
        RETURN json_build_object(
            'success', false,
            'error', 'forbidden',
            'message', 'Users can only delete their own accounts'
        );
    END IF;
    
    -- Get user email for audit log
    SELECT email INTO target_user_email 
    FROM auth.users 
    WHERE id = target_user_id;
    
    IF target_user_email IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'user_not_found',
            'message', 'User not found in auth system'
        );
    END IF;
    
    -- Log the deletion attempt
    INSERT INTO public.user_deletion_audit (
        user_id,
        user_email,
        deletion_reason,
        deleted_by,
        ip_address,
        additional_data
    ) VALUES (
        target_user_id,
        target_user_email,
        'complete_account_deletion',
        current_user_id,
        inet_client_addr(),
        json_build_object(
            'deletion_timestamp', NOW(),
            'method', 'admin_delete_function',
            'includes_auth_deletion', true
        )
    );
    
    -- Delete application data first (in correct order)
    
    -- 1. Delete user purchases
    DELETE FROM public.user_purchases WHERE user_id = target_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 2. Delete user statistics
    DELETE FROM public.user_statistics WHERE user_id = target_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 3. Delete leaderboard entries
    DELETE FROM public.leaderboard_entries WHERE user_id = target_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 4. Delete sync status records
    DELETE FROM public.sync_status WHERE user_id = target_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 5. Delete from auth.users (THIS IS THE KEY ADDITION)
    -- This requires the function to have access to the auth schema
    DELETE FROM auth.users WHERE id = target_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    
    IF temp_row_count = 0 THEN
        -- Auth user deletion failed
        RETURN json_build_object(
            'success', false,
            'error', 'auth_deletion_failed',
            'message', 'Failed to delete user from auth system',
            'app_data_deleted', affected_rows
        );
    END IF;
    
    affected_rows := affected_rows + temp_row_count;
    
    -- Build success response
    deletion_result := json_build_object(
        'success', true,
        'message', 'Account completely deleted including auth record',
        'user_id', target_user_id,
        'user_email', target_user_email,
        'affected_rows', affected_rows,
        'deleted_at', NOW(),
        'auth_user_deleted', true
    );
    
    RETURN deletion_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO public.user_deletion_audit (
            user_id,
            user_email,
            deletion_reason,
            deleted_by,
            additional_data
        ) VALUES (
            target_user_id,
            target_user_email,
            'deletion_failed',
            current_user_id,
            json_build_object(
                'error_message', SQLERRM,
                'error_state', SQLSTATE,
                'deletion_timestamp', NOW(),
                'attempted_complete_deletion', true
            )
        );
        
        -- Return error response
        RETURN json_build_object(
            'success', false,
            'error', 'deletion_failed',
            'message', 'Failed to delete account: ' || SQLERRM,
            'error_code', SQLSTATE,
            'user_id', target_user_id
        );
END;
$$;

-- =====================================================
-- 2. CREATE USER-FACING DELETION FUNCTION
-- =====================================================
-- This function allows users to delete their own accounts

CREATE OR REPLACE FUNCTION public.delete_user_account_complete()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    -- Get the current authenticated user
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'unauthorized',
            'message', 'User must be authenticated to delete account'
        );
    END IF;
    
    -- Call the admin function for self-deletion
    RETURN public.admin_delete_user_account(current_user_id);
END;
$$;

-- =====================================================
-- 3. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant execute permission on the user-facing function
GRANT EXECUTE ON FUNCTION public.delete_user_account_complete() TO authenticated;

-- Grant execute permission on the admin function (restricted)
GRANT EXECUTE ON FUNCTION public.admin_delete_user_account(UUID) TO authenticated;

-- Grant access to auth schema for the admin function
-- NOTE: This requires superuser privileges to execute
-- You may need to run this as a database admin
GRANT USAGE ON SCHEMA auth TO postgres;
GRANT DELETE ON auth.users TO postgres;

-- =====================================================
-- 4. CREATE ALTERNATIVE APPROACH USING SUPABASE ADMIN API
-- =====================================================
-- If direct auth.users deletion doesn't work, use this approach

CREATE OR REPLACE FUNCTION public.mark_user_for_deletion()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_id UUID;
    current_user_email TEXT;
    affected_rows INTEGER := 0;
    temp_row_count INTEGER;
BEGIN
    -- Get the current authenticated user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'unauthorized',
            'message', 'User must be authenticated to delete account'
        );
    END IF;
    
    -- Get user email
    SELECT email INTO current_user_email 
    FROM auth.users 
    WHERE id = current_user_id;
    
    -- Delete application data
    DELETE FROM public.user_purchases WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    DELETE FROM public.user_statistics WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    DELETE FROM public.leaderboard_entries WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    DELETE FROM public.sync_status WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- Log deletion with special flag for admin API processing
    INSERT INTO public.user_deletion_audit (
        user_id,
        user_email,
        deletion_reason,
        deleted_by,
        ip_address,
        additional_data
    ) VALUES (
        current_user_id,
        current_user_email,
        'marked_for_auth_deletion',
        current_user_id,
        inet_client_addr(),
        json_build_object(
            'deletion_timestamp', NOW(),
            'method', 'mark_for_deletion',
            'requires_admin_api_deletion', true,
            'app_data_deleted', true
        )
    );
    
    RETURN json_build_object(
        'success', true,
        'message', 'Account data deleted, auth deletion pending',
        'user_id', current_user_id,
        'user_email', current_user_email,
        'affected_rows', affected_rows,
        'deleted_at', NOW(),
        'auth_deletion_required', true,
        'next_step', 'Admin API will complete auth user deletion'
    );
END;
$$;

-- Grant permission for the marking function
GRANT EXECUTE ON FUNCTION public.mark_user_for_deletion() TO authenticated;

-- =====================================================
-- 5. TESTING FUNCTIONS
-- =====================================================

-- Test complete deletion (use with caution!)
-- SELECT public.delete_user_account_complete();

-- Test marking for deletion
-- SELECT public.mark_user_for_deletion();

-- Check deletion audit logs
-- SELECT * FROM public.user_deletion_audit ORDER BY deleted_at DESC LIMIT 10;

-- =====================================================
-- IMPLEMENTATION NOTES:
-- =====================================================
-- 
-- 1. The admin_delete_user_account() function can delete auth users
--    but requires elevated database privileges
-- 
-- 2. If direct auth deletion fails, use mark_user_for_deletion()
--    and implement admin API deletion in your Flutter app
-- 
-- 3. The Flutter app should call delete_user_account_complete()
--    for complete deletion
-- 
-- 4. Monitor the user_deletion_audit table for failed deletions
--    that need admin API processing
-- 
-- =====================================================
