// =====================================================
// SERVER-SIDE USER DELETION ENDPOINT
// =====================================================
// This Node.js/Express endpoint handles complete user deletion
// including Supabase Auth user removal using admin privileges.
// 
// Deploy this as a serverless function (Vercel, Netlify, etc.)
// or as part of your backend API.
// =====================================================

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase with service role key (admin privileges)
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY, // This key has admin privileges
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Complete user deletion endpoint
 * DELETE /api/delete-user
 * 
 * Headers:
 * - Authorization: Bearer <user_jwt_token>
 * 
 * Body:
 * {
 *   "userId": "user-uuid-here"
 * }
 */
async function deleteUserEndpoint(req, res) {
  try {
    // Verify the request method
    if (req.method !== 'DELETE') {
      return res.status(405).json({
        success: false,
        error: 'method_not_allowed',
        message: 'Only DELETE method is allowed'
      });
    }

    // Get the authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'unauthorized',
        message: 'Authorization header required'
      });
    }

    const userToken = authHeader.substring(7); // Remove 'Bearer ' prefix
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'missing_user_id',
        message: 'userId is required in request body'
      });
    }

    // Verify the user token and get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(userToken);
    
    if (authError || !user) {
      return res.status(401).json({
        success: false,
        error: 'invalid_token',
        message: 'Invalid or expired token'
      });
    }

    // Security check: Users can only delete their own account
    if (user.id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'forbidden',
        message: 'Users can only delete their own accounts'
      });
    }

    console.log(`Starting complete deletion for user: ${userId}`);

    // Step 1: Delete application data using RPC function
    const { data: rpcResult, error: rpcError } = await supabaseAdmin
      .rpc('mark_user_for_deletion');

    if (rpcError) {
      console.error('RPC deletion failed:', rpcError);
      return res.status(500).json({
        success: false,
        error: 'app_data_deletion_failed',
        message: 'Failed to delete application data',
        details: rpcError.message
      });
    }

    console.log('Application data deleted successfully');

    // Step 2: Delete the auth user using admin privileges
    const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (authDeleteError) {
      console.error('Auth user deletion failed:', authDeleteError);
      
      // Log this for manual cleanup
      await logFailedAuthDeletion(userId, user.email, authDeleteError);
      
      return res.status(500).json({
        success: false,
        error: 'auth_deletion_failed',
        message: 'Application data deleted but failed to remove authentication record',
        details: authDeleteError.message,
        app_data_deleted: true,
        requires_manual_cleanup: true
      });
    }

    console.log(`Complete user deletion successful for: ${userId}`);

    // Log successful complete deletion
    await logSuccessfulDeletion(userId, user.email);

    return res.status(200).json({
      success: true,
      message: 'User account completely deleted',
      user_id: userId,
      deleted_at: new Date().toISOString(),
      app_data_deleted: true,
      auth_user_deleted: true
    });

  } catch (error) {
    console.error('User deletion endpoint error:', error);
    
    return res.status(500).json({
      success: false,
      error: 'internal_server_error',
      message: 'An unexpected error occurred during user deletion',
      details: error.message
    });
  }
}

/**
 * Log failed auth deletion for manual cleanup
 */
async function logFailedAuthDeletion(userId, userEmail, error) {
  try {
    await supabaseAdmin
      .from('user_deletion_audit')
      .insert({
        user_id: userId,
        user_email: userEmail,
        deletion_reason: 'auth_deletion_failed',
        deleted_by: userId,
        additional_data: {
          error_message: error.message,
          error_code: error.code,
          deletion_timestamp: new Date().toISOString(),
          method: 'server_endpoint',
          requires_manual_cleanup: true,
          app_data_deleted: true
        }
      });
  } catch (logError) {
    console.error('Failed to log auth deletion failure:', logError);
  }
}

/**
 * Log successful complete deletion
 */
async function logSuccessfulDeletion(userId, userEmail) {
  try {
    await supabaseAdmin
      .from('user_deletion_audit')
      .insert({
        user_id: userId,
        user_email: userEmail,
        deletion_reason: 'complete_deletion_successful',
        deleted_by: userId,
        additional_data: {
          deletion_timestamp: new Date().toISOString(),
          method: 'server_endpoint',
          app_data_deleted: true,
          auth_user_deleted: true,
          complete_deletion: true
        }
      });
  } catch (logError) {
    console.error('Failed to log successful deletion:', logError);
  }
}

// Export for serverless deployment
module.exports = deleteUserEndpoint;

// For Express.js server
// app.delete('/api/delete-user', deleteUserEndpoint);

// =====================================================
// DEPLOYMENT INSTRUCTIONS:
// =====================================================
// 
// 1. Vercel Deployment:
//    - Create /api/delete-user.js with this code
//    - Set environment variables in Vercel dashboard
//    - Deploy and get endpoint URL
// 
// 2. Netlify Deployment:
//    - Create /netlify/functions/delete-user.js
//    - Set environment variables in Netlify dashboard
//    - Deploy and get endpoint URL
// 
// 3. Environment Variables Required:
//    - SUPABASE_URL: Your Supabase project URL
//    - SUPABASE_SERVICE_ROLE_KEY: Service role key (admin privileges)
// 
// 4. Update Flutter app to call this endpoint instead of RPC
// 
// =====================================================

// Example Flutter HTTP call:
/*
Future<void> deleteUserViaAPI() async {
  final user = supabase.auth.currentUser;
  if (user == null) throw Exception('Not authenticated');
  
  final response = await http.delete(
    Uri.parse('https://your-api.vercel.app/api/delete-user'),
    headers: {
      'Authorization': 'Bearer ${user.accessToken}',
      'Content-Type': 'application/json',
    },
    body: json.encode({'userId': user.id}),
  );
  
  final data = json.decode(response.body);
  if (!data['success']) {
    throw Exception(data['message']);
  }
}
*/
