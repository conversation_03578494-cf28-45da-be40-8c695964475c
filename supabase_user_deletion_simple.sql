-- =====================================================
-- SIMPLIFIED SUPABASE USER DELETION FUNCTION
-- =====================================================
-- This is a simplified version that works better with Supabase's
-- transaction management and avoids the "invalid transaction termination" error.
-- 
-- INSTRUCTIONS:
-- 1. Run this SQL in your Supabase SQL editor
-- 2. This will replace the previous delete_user_account function
-- 3. Test with: SELECT public.delete_user_account();
-- =====================================================

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.delete_user_account();

-- Create simplified user account deletion function
CREATE OR REPLACE FUNCTION public.delete_user_account()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_id UUID;
    current_user_email TEXT;
    affected_rows INTEGER := 0;
    temp_row_count INTEGER;
BEGIN
    -- Get the current authenticated user
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'unauthorized',
            'message', 'User must be authenticated to delete account'
        );
    END IF;
    
    -- Get user email for audit log
    SELECT email INTO current_user_email 
    FROM auth.users 
    WHERE id = current_user_id;
    
    -- Log the deletion attempt first
    INSERT INTO public.user_deletion_audit (
        user_id,
        user_email,
        deletion_reason,
        deleted_by,
        ip_address,
        additional_data
    ) VALUES (
        current_user_id,
        current_user_email,
        'user_requested',
        current_user_id,
        inet_client_addr(),
        json_build_object(
            'deletion_timestamp', NOW(),
            'method', 'rpc_function_simple'
        )
    );
    
    -- Delete user-related data in correct order (respecting foreign keys)
    
    -- 1. Delete user purchases
    DELETE FROM public.user_purchases WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 2. Delete user statistics
    DELETE FROM public.user_statistics WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 3. Delete leaderboard entries
    DELETE FROM public.leaderboard_entries WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- 4. Delete sync status records
    DELETE FROM public.sync_status WHERE user_id = current_user_id;
    GET DIAGNOSTICS temp_row_count = ROW_COUNT;
    affected_rows := affected_rows + temp_row_count;
    
    -- Return success response
    RETURN json_build_object(
        'success', true,
        'message', 'Account data deleted successfully',
        'user_id', current_user_id,
        'affected_rows', affected_rows,
        'deleted_at', NOW(),
        'note', 'Auth user will be deleted by Supabase automatically'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error response (let Supabase handle transaction rollback)
        RETURN json_build_object(
            'success', false,
            'error', 'deletion_failed',
            'message', 'Failed to delete account data: ' || SQLERRM,
            'error_code', SQLSTATE,
            'user_id', current_user_id
        );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.delete_user_account() TO authenticated;

-- =====================================================
-- ALTERNATIVE: Step-by-step deletion functions
-- =====================================================
-- If the above function still has issues, you can use these
-- individual functions to delete data step by step

-- Function to delete user purchases
CREATE OR REPLACE FUNCTION public.delete_user_purchases()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'unauthorized');
    END IF;
    
    DELETE FROM public.user_purchases WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN json_build_object(
        'success', true,
        'table', 'user_purchases',
        'deleted_rows', deleted_count
    );
END;
$$;

-- Function to delete user statistics
CREATE OR REPLACE FUNCTION public.delete_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'unauthorized');
    END IF;
    
    DELETE FROM public.user_statistics WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN json_build_object(
        'success', true,
        'table', 'user_statistics',
        'deleted_rows', deleted_count
    );
END;
$$;

-- Function to delete leaderboard entries
CREATE OR REPLACE FUNCTION public.delete_user_leaderboard()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'unauthorized');
    END IF;
    
    DELETE FROM public.leaderboard_entries WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN json_build_object(
        'success', true,
        'table', 'leaderboard_entries',
        'deleted_rows', deleted_count
    );
END;
$$;

-- Function to delete sync status
CREATE OR REPLACE FUNCTION public.delete_user_sync_status()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'unauthorized');
    END IF;
    
    DELETE FROM public.sync_status WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN json_build_object(
        'success', true,
        'table', 'sync_status',
        'deleted_rows', deleted_count
    );
END;
$$;

-- Grant permissions for step-by-step functions
GRANT EXECUTE ON FUNCTION public.delete_user_purchases() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user_leaderboard() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user_sync_status() TO authenticated;

-- =====================================================
-- TESTING INSTRUCTIONS
-- =====================================================
-- 
-- Test the main function:
-- SELECT public.delete_user_account();
--
-- Or test step-by-step functions:
-- SELECT public.delete_user_purchases();
-- SELECT public.delete_user_statistics();
-- SELECT public.delete_user_leaderboard();
-- SELECT public.delete_user_sync_status();
--
-- =====================================================
